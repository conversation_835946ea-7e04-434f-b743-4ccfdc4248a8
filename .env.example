# PentAGI Environment Variables

CORS_ORIGINS=http://localhost:*,https://localhost:*

COOKIE_SIGNING_SALT=salt # change this to improve security

# Allow to interact with user while executing tasks
ASK_USER=

## LLM Providers
OPEN_AI_KEY=
OPEN_AI_SERVER_URL=https://api.openai.com/v1

ANTHROPIC_API_KEY=
ANTHROPIC_SERVER_URL=https://api.anthropic.com/v1

## Custom LLM provider
LLM_SERVER_URL=
LLM_SERVER_KEY=
LLM_SERVER_MODEL=
LLM_SERVER_CONFIG_PATH=
LLM_SERVER_LEGACY_REASONING=

## Embedding
EMBEDDING_URL=
EMBEDDING_KEY=
EMBEDDING_MODEL=
EMBEDDING_PROVIDER=
EMBEDDING_BATCH_SIZE=

## Summarizer
SUMMARIZER_PRESERVE_LAST=
SUMMARIZER_USE_QA=
SUMMARIZER_SUM_MSG_HUMAN_IN_QA=
SUMMARIZER_LAST_SEC_BYTES=
SUMMARIZER_MAX_BP_BYTES=
SUMMARIZER_MAX_QA_SECTIONS=
SUMMARIZER_MAX_QA_BYTES=
SUMMARIZER_KEEP_QA_SECTIONS=

## Assistant
ASSISTANT_USE_AGENTS=
ASSISTANT_SUMMARIZER_PRESERVE_LAST=
ASSISTANT_SUMMARIZER_LAST_SEC_BYTES=
ASSISTANT_SUMMARIZER_MAX_BP_BYTES=
ASSISTANT_SUMMARIZER_MAX_QA_SECTIONS=
ASSISTANT_SUMMARIZER_MAX_QA_BYTES=
ASSISTANT_SUMMARIZER_KEEP_QA_SECTIONS=

## HTTP proxy to use it in isolation environment
PROXY_URL=

## Scraper URLs and settings
SCRAPER_PUBLIC_URL=
SCRAPER_PRIVATE_URL=*********************************/
LOCAL_SCRAPER_USERNAME=someuser
LOCAL_SCRAPER_PASSWORD=somepass
LOCAL_SCRAPER_MAX_CONCURRENT_SESSIONS=10

## Web server settings
PUBLIC_URL=https://localhost:8443
STATIC_DIR=
STATIC_URL=
SERVER_PORT=8443
SERVER_HOST=0.0.0.0
SERVER_SSL_CRT=
SERVER_SSL_KEY=
SERVER_USE_SSL=true

## OAuth google
OAUTH_GOOGLE_CLIENT_ID=
OAUTH_GOOGLE_CLIENT_SECRET=

## OAuth github
OAUTH_GITHUB_CLIENT_ID=
OAUTH_GITHUB_CLIENT_SECRET=

## DuckDuckGo search engine API
DUCKDUCKGO_ENABLED=

## Google search engine API
GOOGLE_API_KEY=
GOOGLE_CX_KEY=

## Traversaal search engine API
TRAVERSAAL_API_KEY=

## Tavily search engine API
TAVILY_API_KEY=

## Perplexity search engine API
PERPLEXITY_API_KEY=
PERPLEXITY_MODEL=
PERPLEXITY_CONTEXT_SIZE=

## SEARXNG search engine API
SEARXNG_URL=
SEARXNG_CATEGORIES=general
SEARXNG_LANGUAGE=en
SEARXNG_SAFESearch=0
SEARXNG_TIME_RANGE=
SEARXNG_PROXY_URL=

## Langfuse observability settings
LANGFUSE_BASE_URL=
LANGFUSE_PROJECT_ID=
LANGFUSE_PUBLIC_KEY=
LANGFUSE_SECRET_KEY=

## OpenTelemetry observability settings
OTEL_HOST=

## Docker client settings to run primary terminal container
DOCKER_HOST=unix:///var/run/docker.sock
DOCKER_TLS_VERIFY=
DOCKER_CERT_PATH=

## Docker settings inside primary terminal container
DOCKER_INSIDE=true # enable to use docker socket
DOCKER_NET_ADMIN=true # enable to use net_admin capability
DOCKER_SOCKET=/var/run/docker.sock # path on host machine
DOCKER_NETWORK=pentagi-network # must be exist
DOCKER_PUBLIC_IP=0.0.0.0 # public ip of host machine
DOCKER_WORK_DIR=
DOCKER_DEFAULT_IMAGE=
DOCKER_DEFAULT_IMAGE_FOR_PENTEST=

# Postgres (pgvector) settings
PENTAGI_POSTGRES_USER=postgres
PENTAGI_POSTGRES_PASSWORD=postgres # change this to improve security
PENTAGI_POSTGRES_DB=pentagidb


# Langfuse Environment Variables

## Langfuse Postgres
LANGFUSE_POSTGRES_USER=postgres
LANGFUSE_POSTGRES_PASSWORD=postgres # change this to improve security
LANGFUSE_POSTGRES_DB=langfuse
LANGFUSE_POSTGRES_VERSION=16

## Langfuse Clickhouse
LANGFUSE_CLICKHOUSE_USER=clickhouse
LANGFUSE_CLICKHOUSE_PASSWORD=clickhouse # change this to improve security
LANGFUSE_CLICKHOUSE_URL=http://langfuse-clickhouse:8123
LANGFUSE_CLICKHOUSE_MIGRATION_URL=clickhouse://langfuse-clickhouse:9000
LANGFUSE_CLICKHOUSE_CLUSTER_ENABLED=false

## Langfuse S3
LANGFUSE_S3_BUCKET=langfuse
LANGFUSE_S3_REGION=auto
LANGFUSE_S3_ACCESS_KEY_ID=accesskey # change this to improve security
LANGFUSE_S3_SECRET_ACCESS_KEY=secretkey # change this to improve security
LANGFUSE_S3_ENDPOINT=http://langfuse-minio:9000
LANGFUSE_S3_FORCE_PATH_STYLE=true
LANGFUSE_S3_EVENT_UPLOAD_PREFIX=events/
LANGFUSE_S3_MEDIA_UPLOAD_PREFIX=media/

## Langfuse Redis
LANGFUSE_REDIS_HOST=langfuse-redis
LANGFUSE_REDIS_PORT=6379
LANGFUSE_REDIS_AUTH=redispassword # change this to improve security

## Langfuse web app security settings
LANGFUSE_SALT=salt # change this to improve security
LANGFUSE_ENCRYPTION_KEY=0000000000000000000000000000000000000000000000000000000000000000 # change this to improve security

## Langfuse web app nextauth settings
LANGFUSE_NEXTAUTH_URL=http://localhost:4000
LANGFUSE_NEXTAUTH_SECRET=secret # change this to improve security

## Langfuse extra settings
LANGFUSE_ENABLE_EXPERIMENTAL_FEATURES=true
LANGFUSE_TELEMETRY_ENABLED=false
LANGFUSE_LOG_LEVEL=info

## Langfuse init settings
LANGFUSE_INIT_ORG_ID=ocm47619l0000872mcd2dlbqwb
LANGFUSE_INIT_ORG_NAME=PentAGI Org
LANGFUSE_INIT_PROJECT_ID=cm47619l0000872mcd2dlbqwb
LANGFUSE_INIT_PROJECT_NAME=PentAGI
LANGFUSE_INIT_PROJECT_PUBLIC_KEY=pk-lf-00000000-0000-0000-0000-000000000000 # change this to improve security
LANGFUSE_INIT_PROJECT_SECRET_KEY=sk-lf-00000000-0000-0000-0000-000000000000 # change this to improve security
LANGFUSE_INIT_USER_EMAIL=<EMAIL>
LANGFUSE_INIT_USER_NAME=admin
LANGFUSE_INIT_USER_PASSWORD=password # change this to improve security

## Langfuse SDK sync settings
LANGFUSE_SDK_CI_SYNC_PROCESSING_ENABLED=false
LANGFUSE_READ_FROM_POSTGRES_ONLY=false
LANGFUSE_READ_FROM_CLICKHOUSE_ONLY=true
LANGFUSE_RETURN_FROM_CLICKHOUSE=true

## Langfuse license settings
LANGFUSE_EE_LICENSE_KEY=

## Langfuse OpenTelemetry settings
LANGFUSE_OTEL_EXPORTER_OTLP_ENDPOINT=
LANGFUSE_OTEL_SERVICE_NAME=

## Langfuse custom oauth2 settings
LANGFUSE_AUTH_CUSTOM_CLIENT_ID=
LANGFUSE_AUTH_CUSTOM_CLIENT_SECRET=
LANGFUSE_AUTH_CUSTOM_ISSUER=
LANGFUSE_AUTH_CUSTOM_NAME=PentAGI
LANGFUSE_AUTH_CUSTOM_SCOPE=openid email profile
LANGFUSE_AUTH_CUSTOM_CLIENT_AUTH_METHOD=client_secret_post
LANGFUSE_AUTH_CUSTOM_ALLOW_ACCOUNT_LINKING=true

## Langfuse auth settings
LANGFUSE_AUTH_DISABLE_SIGNUP=false # disable signup if PentAGI OAuth2 is used
LANGFUSE_AUTH_SESSION_MAX_AGE=240

## Langfuse allowed organization creators
LANGFUSE_ALLOWED_ORGANIZATION_CREATORS=<EMAIL>

## Langfuse default settings for new users
LANGFUSE_DEFAULT_ORG_ID=ocm47619l0000872mcd2dlbqwb
LANGFUSE_DEFAULT_PROJECT_ID=cm47619l0000872mcd2dlbqwb
LANGFUSE_DEFAULT_ORG_ROLE=VIEWER
LANGFUSE_DEFAULT_PROJECT_ROLE=VIEWER
